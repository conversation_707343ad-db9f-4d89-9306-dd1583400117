<?php echo $__env->make('partials.input',['name'=>'Name','id'=>"name",'placeholder'=>"Plan name",'required'=>true,'value'=>(isset($plan)?$plan->name:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<div class="row">
    <div class="col-md-12">
        <?php echo $__env->make('partials.input',['name'=>'Plan description','id'=>"description",'placeholder'=>"Plan description...",'required'=>false,'value'=>(isset($plan)?$plan->description:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
    <div class="col-md-12">
        <div class="form-group<?php echo e($errors->has('features') ? ' has-danger' : ''); ?>">
            <label class="form-control-label" for="features"><?php echo e(__('Features list')); ?></label>
            <textarea name="features" id="features" rows="16" class="form-control<?php echo e($errors->has('features') ? ' is-invalid' : ''); ?>" placeholder="Enter features with headings and separators..."><?php echo e(old('features') ? old('features') : (isset($plan) ? $plan->features : null)); ?></textarea>
            <small class="text-muted">
                <strong><?php echo e(__('Feature Organization Guide:')); ?></strong><br>
                • Use <code>##HEADING##</code> to create feature category headings<br>
                • Separate individual features with commas<br>
                • Example: <code>##Core Messaging##, 25000 messages included, Shared Team Inbox for centralized, ##Advanced AI & Automation##, AI Chat Summary, Advanced chatbot flows</code>
            </small>
            <?php if($errors->has('features')): ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($errors->first('features')); ?></strong>
                </span>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php echo $__env->make('partials.input',['type'=>'number','name'=>'Price','id'=>"price",'placeholder'=>"Plan prce",'required'=>true,'value'=>(isset($plan)?$plan->price:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php if(config('settings.enable_credits')): ?>
    <div style="width: 50%;">
        <?php echo $__env->make('partials.input',['class'=>'','additionalInfo'=>'Number of credits that will be added to the user\'s account when they subscribe to this plan, on the interval selected below','type'=>'number','name'=>'Credit amount','id'=>"credit_amount",'placeholder'=>"Plan credit amount",'required'=>true,'value'=>(isset($plan)?$plan->credit_amount:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
<?php endif; ?>

<div class="row">
    <!-- THIS IS SPECIAL -->
    <div class="col-md-6">
        <label class="form-control-label"><?php echo e(__("Plan period")); ?></label>
        <div class="custom-control custom-radio mb-3">
            <input name="period" class="custom-control-input" id="monthly"  <?php if(isset($plan)): ?>  <?php if($plan->period == 1): ?> checked <?php endif; ?> <?php else: ?> checked <?php endif; ?>  value="monthly" type="radio">
            <label class="custom-control-label" for="monthly"><?php echo e(__('Monthly')); ?></label>
        </div>
        <div class="custom-control custom-radio mb-3">
            <input name="period" class="custom-control-input" id="anually" value="anually" <?php if(isset($plan) && $plan->period == 2): ?> checked <?php endif; ?> type="radio">
            <label class="custom-control-label" for="anually"><?php echo e(__('Anually')); ?></label>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12 mt-4"><h6 class="heading text-muted mb-4"><?php echo e(__('Payment processor')); ?></h6></div>
    <?php if(config('settings.subscription_processor',"Stripe")=='Stripe'): ?>
    <div class="col-md-6">
        <?php echo $__env->make('partials.input',['name'=>'Stripe Pricing Plan ID','id'=>"stripe_id",'placeholder'=>"Product price plan id from Stripe starting with price_xxxxxx",'required'=>false,'value'=>(isset($plan)?$plan->stripe_id:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
<?php else: ?>
    <?php if(strtolower(config('settings.subscription_processor'))!='local'): ?>
        <?php echo $__env->make($theSelectedProcessor."-subscribe::planid", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
<?php endif; ?>
</div>

<div class="row">
    
    <div class="col-12 mt-4"><h6 class="heading text-muted mb-4"><?php echo e(__('Plan limits')); ?></h6></div>
    <?php if(config('settings.limit_items_show',true)): ?>
        <div class="col-md-6">
            <?php echo $__env->make('partials.input',['type'=>"number", 'name'=>config('settings.limit_items_name',"Limit items"),'id'=>"limit_items",'placeholder'=>"Number of allowed usage",'required'=>false,'additionalInfo'=>"0 is unlimited numbers of usage per plan period",'value'=>(isset($plan)?$plan->limit_items:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    <?php endif; ?>

    <?php if(config('settings.limit_views_show',false)): ?>
        <div class="col-md-6">
            <?php echo $__env->make('partials.input',['type'=>"number", 'name'=>config('settings.limit_views_name',"Limit views"),'id'=>"limit_views",'placeholder'=>"Number of allowed usage",'required'=>false,'additionalInfo'=>"0 is unlimited numbers of usage per plan period",'value'=>(isset($plan)?$plan->limit_views:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
   <?php endif; ?>

   <?php if(config('settings.limit_orders_show',false)): ?>
        <div class="col-md-6">
            <?php echo $__env->make('partials.input',['type'=>"number", 'name'=>config('settings.limit_orders_name',"Limit orders"),'id'=>"limit_orders",'placeholder'=>"Number of allowed usage",'required'=>false,'additionalInfo'=>"0 is unlimited numbers of usage per plan period",'value'=>(isset($plan)?$plan->limit_orders:null)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
   <?php endif; ?>
   
   

</div>
   
<input name="ordering" value="enabled" type="hidden" /> 

<?php echo $__env->make('plans.plugins', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<div class="text-center">
    <button type="submit" class="btn btn-success mt-4"><?php echo e(isset($plan)?__('Update plan'):__('SAVE')); ?></button>
</div>
<?php /**PATH C:\xampp\htdocs\zaptra\resources\views/plans/form.blade.php ENDPATH**/ ?>